package com.jurassic.myhealth.home.data.repository

import com.jurassic.myhealth.home.data.api.MyHealthApiService
import com.jurassic.myhealth.home.data.model.LuxmedSyncRequest
import com.jurassic.myhealth.home.domain.model.LuxmedAuthData
import com.jurassic.myhealth.home.domain.repository.HealthDataRepository
import com.jurassic.myhealth.home.domain.repository.MedicalRecordsRepository

class HealthDataRepositoryImpl(
    private val apiService: MyHealthApiService,
    private val authenticationProvider: AuthenticationProvider
) : HealthDataRepository, MedicalRecordsRepository {

    override suspend fun syncLuxmedData(luxmedAuth: LuxmedAuthData): Result<String> {
        return try {
            // Get MyHealth authentication token
            val authToken = authenticationProvider.getMyHealthToken()
                ?: return Result.failure(Exception("User not authenticated or failed to get auth token"))
            
            // Prepare request
            val request = LuxmedSyncRequest(
                jwtToken = luxmedAuth.jwtToken,
                aspNetSessionId = luxmedAuth.aspNetSessionId,
                lxToken = luxmedAuth.lxToken,
                refreshToken = luxmedAuth.refreshToken,
                userAdditionalInfo = luxmedAuth.userAdditionalInfo,
                xsrfToken = luxmedAuth.xsrfToken,
                incapsulaSessionId = luxmedAuth.incapsulaSessionId,
                deviceId = luxmedAuth.deviceId
            )
            
            // Make API call
            apiService.syncLuxmedData("Bearer $authToken", request)
                .fold(
                    onSuccess = { response ->
                        if (response.success) {
                            Result.success(response.message)
                        } else {
                            Result.failure(Exception(response.message))
                        }
                    },
                    onFailure = { error ->
                        Result.failure(error)
                    }
                )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override fun fetchAllMedicalRecords(authenticationToken: String): Result<Unit> {
        // TODO: Implement medical records fetching
        return Result.success(Unit)
    }
}
