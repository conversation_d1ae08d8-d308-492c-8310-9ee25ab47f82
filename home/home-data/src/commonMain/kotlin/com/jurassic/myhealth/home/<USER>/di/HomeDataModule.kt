package com.jurassic.myhealth.home.data.di

import com.jurassic.myhealth.home.data.api.MyHealthApiService
import com.jurassic.myhealth.home.data.api.MyHealthApiServiceImpl
import com.jurassic.myhealth.home.data.network.HttpClientFactory
import com.jurassic.myhealth.home.data.repository.HealthDataRepositoryImpl
import com.jurassic.myhealth.home.domain.repository.HealthDataRepository
import com.jurassic.myhealth.home.domain.repository.MedicalRecordsRepository
import dev.gitlive.firebase.auth.FirebaseAuth
import io.ktor.client.HttpClient
import org.koin.dsl.module

val homeDataModule = module {

    single<HttpClient> {
        HttpClientFactory.create()
    }

    single<MyHealthApiService> {
        MyHealthApiServiceImpl(get())
    }

    single<HealthDataRepository> {
        val firebaseAuth: FirebaseAuth = get()
        HealthDataRepositoryImpl(
            apiService = get(),
            getFirebaseToken = {
                try {
                    firebaseAuth.currentUser?.getIdToken(false)
                } catch (e: Exception) {
                    null
                }
            }
        )
    }

    single<MedicalRecordsRepository> {
        get<HealthDataRepository>() as HealthDataRepositoryImpl
    }
}
