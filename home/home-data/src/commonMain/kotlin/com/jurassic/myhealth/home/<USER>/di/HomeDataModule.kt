package com.jurassic.myhealth.home.data.di

import com.jurassic.myhealth.home.data.api.MyHealthApiService
import com.jurassic.myhealth.home.data.api.MyHealthApiServiceImpl
import com.jurassic.myhealth.home.data.network.HttpClientFactory
import com.jurassic.myhealth.home.data.repository.HealthDataRepositoryImpl
import com.jurassic.myhealth.home.domain.repository.HealthDataRepository
import com.jurassic.myhealth.home.domain.repository.MedicalRecordsRepository
import io.ktor.client.HttpClient
import org.koin.dsl.module

val homeDataModule = module {

    single<HttpClient> {
        HttpClientFactory.create()
    }

    factory<MyHealthApiService> {
        MyHealthApiServiceImpl(get())
    }

    factory<HealthDataRepository> {
        HealthDataRepositoryImpl(get(), get())
    }
}
