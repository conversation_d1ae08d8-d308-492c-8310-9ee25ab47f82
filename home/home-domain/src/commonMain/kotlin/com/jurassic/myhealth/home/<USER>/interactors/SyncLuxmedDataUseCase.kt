package com.jurassic.myhealth.home.domain.interactors

import com.jurassic.myhealth.home.domain.model.LuxmedAuthData
import com.jurassic.myhealth.home.domain.repository.HealthDataRepository

class SyncLuxmedDataUseCase(
    private val healthDataRepository: HealthDataRepository
) {
    suspend fun execute(luxmedAuth: LuxmedAuthData): Result<String> {
        return healthDataRepository.syncLuxmedData(luxmedAuth)
    }
}
