package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.mapper

import com.jurassic.myhealth.home.domain.model.LuxmedAuthData
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication

fun LuxMedAuthentication.toDomain(): LuxmedAuthData {
    return LuxmedAuthData(
        jwtToken = jwtToken,
        aspNetSessionId = aspNetSessionId,
        lxToken = lxToken,
        refreshToken = refreshToken,
        userAdditionalInfo = userAdditionalInfo,
        xsrfToken = xsrfToken,
        incapsulaSessionId = incapsulaSessionId,
        deviceId = deviceId
    )
}
