package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jurassic.myhealth.common.util.AppLogger
import com.jurassic.myhealth.home.domain.interactors.SyncLuxmedDataUseCase
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.mapper.LuxmedAuthMapper
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LoginProviderContract
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.SyncState
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class LoginProviderViewModel(
    private val syncLuxmedDataUseCase: SyncLuxmedDataUseCase
) : ViewModel() {

    private val _effect: Channel<LoginProviderContract.Effect> = Channel()

    val effect = _effect.receiveAsFlow()
    var uiState = mutableStateOf(LoginProviderContract.UiState())
        private set

    fun onAuthenticationTokenReceived(authenticationToken: LuxMedAuthentication) {
        AppLogger.d("LoginProviderViewModel", "Authentication: $authenticationToken")
        syncLuxmedData(authenticationToken)
    }

    private fun syncLuxmedData(authenticationToken: LuxMedAuthentication) {
        viewModelScope.launch {
            // Update UI state to loading
            uiState.value = uiState.value.copy(syncState = SyncState.Loading)

            syncLuxmedDataUseCase.execute(authenticationToken.toDomain())
                .onSuccess { message ->
                    uiState.value = uiState.value.copy(syncState = SyncState.Success(message))
                    _effect.trySend(LoginProviderContract.Effect.ShowMessage("Sync successful: $message"))
                    _effect.trySend(LoginProviderContract.Effect.NavigateToHome)
                }
                .onFailure { error ->
                    val errorMessage = error.message ?: "Unknown error occurred"
                    uiState.value = uiState.value.copy(syncState = SyncState.Error(errorMessage))
                    _effect.trySend(LoginProviderContract.Effect.ShowMessage("Sync failed: $errorMessage"))
                    AppLogger.e("LoginProviderViewModel", "Sync failed", error)
                }
        }
    }
}