package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import com.jurassic.myhealth.common.util.AppLogger
import com.jurassic.myhealth.home.presentation.feature.provider.feature.add.model.AddProviderContract
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow

class LoginProviderViewModel : ViewModel() {

    private val _effect: Channel<AddProviderContract.Effect> = Channel()

    val effect = _effect.receiveAsFlow()
    var uiState = mutableStateOf(AddProviderContract.UiState())
        private set

    fun onAuthenticationTokenReceived(authenticationToken: LuxMedAuthentication) {
        AppLogger.d("LoginProviderViewModel", "Authentication: $authenticationToken")
    }
}